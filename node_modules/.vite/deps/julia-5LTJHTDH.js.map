{"version": 3, "sources": ["../../@shikijs/langs/dist/julia.mjs"], "sourcesContent": ["import cpp from './cpp.mjs'\nimport python from './python.mjs'\nimport javascript from './javascript.mjs'\nimport r from './r.mjs'\nimport sql from './sql.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Julia\\\",\\\"name\\\":\\\"julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#operator\\\"},{\\\"include\\\":\\\"#array\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#bracket\\\"},{\\\"include\\\":\\\"#function_decl\\\"},{\\\"include\\\":\\\"#function_call\\\"},{\\\"include\\\":\\\"#for_block\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#type_decl\\\"},{\\\"include\\\":\\\"#symbol\\\"},{\\\"include\\\":\\\"#punctuation\\\"}],\\\"repository\\\":{\\\"array\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"end\\\":\\\"(])(\\\\\\\\.?'*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transpose.julia\\\"}},\\\"name\\\":\\\"meta.array.julia\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bbegin\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\bend\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.julia\\\"},{\\\"include\\\":\\\"#self_no_for_block\\\"}]}]},\\\"bracket\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"end\\\":\\\"(})(\\\\\\\\.?'*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transpose.julia\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#self_no_for_block\\\"}]}]},\\\"comment\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_block\\\"},{\\\"begin\\\":\\\"#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.julia\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_tags\\\"}]}]},\\\"comment_block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"#=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.julia\\\"}},\\\"end\\\":\\\"=#\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.julia\\\"}},\\\"name\\\":\\\"comment.block.number-sign-equals.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_tags\\\"},{\\\"include\\\":\\\"#comment_block\\\"}]}]},\\\"comment_tags\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bTODO\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.comment-annotation.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\bFIXME\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.comment-annotation.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\bCHANGED\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.comment-annotation.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\bXXX\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.comment-annotation.julia\\\"}]},\\\"for_block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(for)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.julia\\\"}},\\\"end\\\":\\\"(?<![,\\\\\\\\s])(\\\\\\\\s*\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bouter\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.julia\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"function_call\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)(\\\\\\\\{(?:[^{}]|\\\\\\\\{(?:[^{}]|\\\\\\\\{[^{}]*})*})*})?\\\\\\\\.?(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.julia\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"end\\\":\\\"\\\\\\\\)(('|(\\\\\\\\.'))*\\\\\\\\.?')?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.transposed-func.julia\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#self_no_for_block\\\"}]}]},\\\"function_decl\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.julia\\\"}},\\\"match\\\":\\\"([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)(\\\\\\\\{(?:[^{}]|\\\\\\\\{(?:[^{}]|\\\\\\\\{[^{}]*})*})*})?(?=\\\\\\\\([^#]*\\\\\\\\)(::\\\\\\\\S+)?(\\\\\\\\s*\\\\\\\\bwhere\\\\\\\\b\\\\\\\\s+.+?)?\\\\\\\\s*?=(?![=>]))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.dots.julia\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.julia\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.type.julia\\\"}},\\\"match\\\":\\\"\\\\\\\\b(function|macro)(?:\\\\\\\\s+(?:[_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*(\\\\\\\\.))?([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)(\\\\\\\\{(?:[^{}]|\\\\\\\\{(?:[^{}]|\\\\\\\\{[^{}]*})*})*})?|\\\\\\\\s*)(?=\\\\\\\\()\\\"}]},\\\"keyword\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(?<![.:_])(?:function|mutable\\\\\\\\s+struct|struct|macro|quote|abstract\\\\\\\\s+type|primitive\\\\\\\\s+type|module|baremodule|where)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])(?:if|else|elseif|for|while|begin|let|do|try|catch|finally|return|break|continue)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])end\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.end.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])(?:global|local|const)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.storage.modifier.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])export\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.export.julia\\\"},{\\\"match\\\":\\\"^public\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.public.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])import\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.import.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])using\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.using.julia\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\S\\\\\\\\s+)\\\\\\\\b(as)\\\\\\\\b(?=\\\\\\\\s+\\\\\\\\S)\\\",\\\"name\\\":\\\"keyword.control.as.julia\\\"},{\\\"match\\\":\\\"@(\\\\\\\\.|[_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*|[[\\\\\\\\p{S}\\\\\\\\p{P}]&&[^@\\\\\\\\s]]+)\\\",\\\"name\\\":\\\"support.function.macro.julia\\\"}]},\\\"number\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.conjugate-number.julia\\\"}},\\\"match\\\":\\\"((?<![!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]])\\\\\\\\b(?:0[Xx]\\\\\\\\h(?:_?\\\\\\\\h)*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:[0-9](?:_?[0-9])*\\\\\\\\.?(?!\\\\\\\\.)[0-9_]*|\\\\\\\\.[0-9](?:_?[0-9])*)(?:[Eef][-+]?[0-9](?:_?[0-9])*)?(?:(?:im|Inf(?:16|32|64)?|NaN(?:16|32|64)?|π|pi|ℯ)\\\\\\\\b)?|[0-9]+|Inf(?:16|32|64)?\\\\\\\\b|NaN(?:16|32|64)?\\\\\\\\b|π\\\\\\\\b|pi\\\\\\\\b|ℯ\\\\\\\\b))('*)\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:ARGS|C_NULL|DEPOT_PATH|ENDIAN_BOM|ENV|LOAD_PATH|PROGRAM_FILE|stdin|stdout|stderr|VERSION|devnull)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.global.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:true|false|nothing|missing)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.julia\\\"}]},\\\"operator\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.?(?:<-->|->|-->|<--|[←→↔↚-↞↠↢↣↤↦↩-↬↮↶↷↺-↽⇀⇁⇄⇆⇇⇉⇋-⇐⇒⇔⇚-⇝⇠⇢⇴⇶-⇿⟵⟶⟷⟹-⟿⤀-⤇⤌-⤑⤔-⤘⤝-⤠⥄-⥈⥊⥋⥎⥐⥒⥓⥖⥗⥚⥛⥞⥟⥢⥤⥦-⥭⥰⥷⥺⧴⬰-⭄⭇-⭌￩￫]|=>)\\\",\\\"name\\\":\\\"keyword.operator.arrow.julia\\\"},{\\\"match\\\":\\\":=|\\\\\\\\+=|-=|\\\\\\\\*=|//=|/=|\\\\\\\\.//=|\\\\\\\\./=|\\\\\\\\.\\\\\\\\*=|\\\\\\\\\\\\\\\\=|\\\\\\\\.\\\\\\\\\\\\\\\\=|\\\\\\\\^=|\\\\\\\\.\\\\\\\\^=|%=|\\\\\\\\.%=|÷=|\\\\\\\\.÷=|\\\\\\\\|=|&=|\\\\\\\\.&=|⊻=|\\\\\\\\.⊻=|\\\\\\\\$=|<<=|>>=|>>>=|=(?!=)\\\",\\\"name\\\":\\\"keyword.operator.update.julia\\\"},{\\\"match\\\":\\\"<<|>>>?|\\\\\\\\.>>>?|\\\\\\\\.<<\\\",\\\"name\\\":\\\"keyword.operator.shift.julia\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.relation.types.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.julia\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.transpose.julia\\\"}},\\\"match\\\":\\\"\\\\\\\\s*([:<>]:)\\\\\\\\s*((?:Union)?\\\\\\\\([^)]*\\\\\\\\)|[$_∇[:alpha:]][!.′⁺-ₜ[:word:]]*(?:\\\\\\\\{(?:[^{}]|\\\\\\\\{(?:[^{}]|\\\\\\\\{[^{}]*})*})*}|\\\\\\\".+?(?<!\\\\\\\\\\\\\\\\)\\\\\\\")?)(?:\\\\\\\\.\\\\\\\\.\\\\\\\\.)?(\\\\\\\\.?'*)\\\"},{\\\"match\\\":\\\"(\\\\\\\\.?((?<!<)<=|(?<!>)>=|[<>≤≥]|===?|≡|!=|≠|!==|[∈-∍∝∥∦∷∺∻∽∾≁-≎≐-≓≖-≟≢≣≦-⊋⊏-⊒⊜⊢⊣⊩⊬⊮⊰-⊷⋍⋐⋑⋕-⋭⋲-⋿⟂⟈⟉⟒⦷⧀⧁⧡⧣⧤⧥⩦⩧⩪-⩳⩵-⫙⫪⫫⫷-⫺]|<:|>:))\\\",\\\"name\\\":\\\"keyword.operator.relation.julia\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\s)\\\\\\\\?(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.operator.ternary.julia\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\s):(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.operator.ternary.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\\\\\\||&&|(?<![!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]])!\\\",\\\"name\\\":\\\"keyword.operator.boolean.julia\\\"},{\\\"match\\\":\\\"(?<=[]!)}′⁺-ₜ∇[:word:]]):\\\",\\\"name\\\":\\\"keyword.operator.range.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\|>\\\",\\\"name\\\":\\\"keyword.operator.applies.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\||\\\\\\\\.\\\\\\\\||&|\\\\\\\\.&|[~¬]|\\\\\\\\.~|⊻|\\\\\\\\.⊻\\\",\\\"name\\\":\\\"keyword.operator.bitwise.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\.?(?:\\\\\\\\+\\\\\\\\+|--|[-*+|¦±−∓∔∨∪∸≏⊎⊔⊕⊖⊞⊟⊻⊽⋎⋓⟇⧺⧻⨈⨢-⨮⨹⨺⩁⩂⩅⩊⩌⩏⩐⩒⩔⩖⩗⩛⩝⩡⩢⩣]|//?|[%\\\\\\\\&\\\\\\\\\\\\\\\\^±·×÷·⅋↑↓⇵∓∗-∜∤∧∩≀⊍⊓⊗-⊛⊠⊡⊼⋄-⋇⋉-⋌⋏⋒⌿▷⟑⟕⟖⟗⟰⟱⤈-⤋⤒⤓⥉⥌⥍⥏⥑⥔⥕⥘⥙⥜⥝⥠⥡⥣⥥⥮⥯⦸⦼⦾⦿⧶⧷⨇⨝⨟⨰-⨸⨻⨼⨽⩀⩃⩄⩋⩍⩎⩑⩓⩕⩘⩚⩜⩞⩟⩠⫛￪￬])\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.julia\\\"},{\\\"match\\\":\\\"∘\\\",\\\"name\\\":\\\"keyword.operator.compose.julia\\\"},{\\\"match\\\":\\\"::|(?<=\\\\\\\\s)isa(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.operator.isa.julia\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\s)in(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.operator.relation.in.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\.(?=[@_\\\\\\\\p{L}])|\\\\\\\\.\\\\\\\\.+|[…⁝⋮-⋱]\\\",\\\"name\\\":\\\"keyword.operator.dots.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\$(?=.+)\\\",\\\"name\\\":\\\"keyword.operator.interpolation.julia\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transposed-variable.julia\\\"}},\\\"match\\\":\\\"([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)(('|(\\\\\\\\.'))*\\\\\\\\.?')\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"bracket.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transposed-matrix.julia\\\"}},\\\"match\\\":\\\"(])((?:\\\\\\\\.??')*\\\\\\\\.?')\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"bracket.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transposed-parens.julia\\\"}},\\\"match\\\":\\\"(\\\\\\\\))((?:\\\\\\\\.??')*\\\\\\\\.?')\\\"}]},\\\"parentheses\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\\))(\\\\\\\\.?'*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transpose.julia\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#self_no_for_block\\\"}]}]},\\\"punctuation\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.comma.julia\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.separator.semicolon.julia\\\"}]},\\\"self_no_for_block\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#operator\\\"},{\\\"include\\\":\\\"#array\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#bracket\\\"},{\\\"include\\\":\\\"#function_decl\\\"},{\\\"include\\\":\\\"#function_call\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#type_decl\\\"},{\\\"include\\\":\\\"#symbol\\\"},{\\\"include\\\":\\\"#punctuation\\\"}]},\\\"string\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(@doc)\\\\\\\\s((?:doc)?\\\\\\\"\\\\\\\"\\\\\\\")|(doc\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\"\\\\\\\"\\\\\\\") ?(->)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.arrow.julia\\\"}},\\\"name\\\":\\\"string.docstring.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(i?cxx)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"contentName\\\":\\\"meta.embedded.inline.cpp\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"embed.cxx.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cpp#root_context\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(py)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"contentName\\\":\\\"meta.embedded.inline.python\\\",\\\"end\\\":\\\"([\\\\\\\\s\\\\\\\\w]*)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"embed.python.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(js)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"contentName\\\":\\\"meta.embedded.inline.javascript\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"embed.js.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(R)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"contentName\\\":\\\"meta.embedded.inline.r\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"embed.R.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(raw)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"string.quoted.other.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(raw)(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"string.quoted.other.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(sql)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"contentName\\\":\\\"meta.embedded.inline.sql\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"embed.sql.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.sql\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"var\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.other.symbol.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"var\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.other.symbol.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s?(doc)?(\\\\\\\"\\\\\\\"\\\\\\\")\\\\\\\\s?$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"string.docstring.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"'(?!')\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"string.quoted.single.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.multiline.begin.julia\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.multiline.end.julia\\\"}},\\\"name\\\":\\\"string.quoted.triple.double.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"(?!\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"string.quoted.double.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"r\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.regexp.begin.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\"\\\\\\\"\\\\\\\")([imsx]{0,4})?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.regexp.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.option-toggle.regexp.julia\\\"}},\\\"name\\\":\\\"string.regexp.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"r\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.regexp.begin.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\")([imsx]{0,4})?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.regexp.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.option-toggle.regexp.julia\\\"}},\\\"name\\\":\\\"string.regexp.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(?<!\\\\\\\")([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"},\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\"\\\\\\\"\\\\\\\")([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"name\\\":\\\"string.quoted.other.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(?<!\\\\\\\")([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"},\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"end\\\":\\\"(?<![^\\\\\\\\\\\\\\\\]\\\\\\\\\\\\\\\\)(\\\\\\\")([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"name\\\":\\\"string.quoted.other.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(?<!`)([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)?```\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"},\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"end\\\":\\\"(```)([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"name\\\":\\\"string.interpolated.backtick.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(?<!`)([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)?`\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"},\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"end\\\":\\\"(?<![^\\\\\\\\\\\\\\\\]\\\\\\\\\\\\\\\\)(`)([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"name\\\":\\\"string.interpolated.backtick.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]}]},\\\"string_dollar_sign_interpolate\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\$[_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}[^←-⇿\\\\\\\\P{So}][^$\\\\\\\\P{Sc}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}][^$\\\\\\\\P{Sc}]]*\\\",\\\"name\\\":\\\"variable.interpolation.julia\\\"},{\\\"begin\\\":\\\"\\\\\\\\$(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"name\\\":\\\"variable.interpolation.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#self_no_for_block\\\"}]}]},\\\"string_escaped_char\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\\\\\\\\\|[0-3]\\\\\\\\d{0,2}|[4-7]\\\\\\\\d?|x\\\\\\\\h{0,2}|u\\\\\\\\h{0,4}|U\\\\\\\\h{0,8}|.)\\\",\\\"name\\\":\\\"constant.character.escape.julia\\\"}]},\\\"symbol\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![]!)}′⁺-ₜ∇[:word:]]):[_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*(?![!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]])(?![\\\\\\\"`])\\\",\\\"name\\\":\\\"constant.other.symbol.julia\\\"}]},\\\"type_decl\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.inherited-class.julia\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.inheritance.julia\\\"}},\\\"match\\\":\\\"!:_(?:struct|mutable\\\\\\\\s+struct|abstract\\\\\\\\s+type|primitive\\\\\\\\s+type)\\\\\\\\s+([_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*)(\\\\\\\\s*(<:)\\\\\\\\s*[_ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:alpha:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^←-⇿\\\\\\\\P{So}]][!_′-‷⁗ⁱ-⁾₁-₎℘℮⅀-⅄∂∅∆∇∎-∑∞-∢∫-∳∿⊤⊥⊾-⋃◸-◿♯⟀⟁⟘⟙⦛-⦴⨀-⨆⨉-⨖⨛⨜゛゜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃𝟎-𝟡[:word:]\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}[^\\\\\\\\x01-¡\\\\\\\\P{Mn}][^\\\\\\\\x01-¡\\\\\\\\P{Mc}][^\\\\\\\\x01-¡\\\\\\\\D][^\\\\\\\\x01-¡\\\\\\\\P{Pc}][^\\\\\\\\x01-¡\\\\\\\\P{Sk}][^\\\\\\\\x01-¡\\\\\\\\P{Me}][^\\\\\\\\x01-¡\\\\\\\\P{No}][^←-⇿\\\\\\\\P{So}]]*(?:\\\\\\\\{.*})?)?\\\",\\\"name\\\":\\\"meta.type.julia\\\"}]}},\\\"scopeName\\\":\\\"source.julia\\\",\\\"embeddedLangs\\\":[\\\"cpp\\\",\\\"python\\\",\\\"javascript\\\",\\\"r\\\",\\\"sql\\\"],\\\"aliases\\\":[\\\"jl\\\"]}\"))\n\nexport default [\n...cpp,\n...python,\n...javascript,\n...r,\n...sql,\nlang\n]\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,OAAO,OAAO,OAAO,KAAK,MAAM,ujyBAAk61B,CAAC;AAEz81B,IAAO,gBAAQ;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACA;", "names": []}