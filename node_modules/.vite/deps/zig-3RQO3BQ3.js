import "./chunk-G3PMV62Z.js";

// node_modules/@shikijs/langs/dist/zig.mjs
var lang = Object.freeze(JSON.parse(`{"displayName":"Zig","fileTypes":["zig","zon"],"name":"zig","patterns":[{"include":"#comments"},{"include":"#strings"},{"include":"#keywords"},{"include":"#operators"},{"include":"#punctuation"},{"include":"#numbers"},{"include":"#support"},{"include":"#variables"}],"repository":{"commentContents":{"patterns":[{"match":"\\\\b(TODO|FIXME|XXX|NOTE)\\\\b:?","name":"keyword.todo.zig"}]},"comments":{"patterns":[{"begin":"//[!/](?=[^/])","end":"$","name":"comment.line.documentation.zig","patterns":[{"include":"#commentContents"}]},{"begin":"//","end":"$","name":"comment.line.double-slash.zig","patterns":[{"include":"#commentContents"}]}]},"keywords":{"patterns":[{"match":"\\\\binline\\\\b(?!\\\\s*\\\\bfn\\\\b)","name":"keyword.control.repeat.zig"},{"match":"\\\\b(while|for)\\\\b","name":"keyword.control.repeat.zig"},{"match":"\\\\b(extern|packed|export|pub|noalias|inline|comptime|volatile|align|linksection|threadlocal|allowzero|noinline|callconv)\\\\b","name":"keyword.storage.zig"},{"match":"\\\\b(struct|enum|union|opaque)\\\\b","name":"keyword.structure.zig"},{"match":"\\\\b(asm|unreachable)\\\\b","name":"keyword.statement.zig"},{"match":"\\\\b(break|return|continue|defer|errdefer)\\\\b","name":"keyword.control.flow.zig"},{"match":"\\\\b(resume|suspend|nosuspend)\\\\b","name":"keyword.control.async.zig"},{"match":"\\\\b(try|catch)\\\\b","name":"keyword.control.trycatch.zig"},{"match":"\\\\b(if|else|switch|orelse)\\\\b","name":"keyword.control.conditional.zig"},{"match":"\\\\b(null|undefined)\\\\b","name":"keyword.constant.default.zig"},{"match":"\\\\b(true|false)\\\\b","name":"keyword.constant.bool.zig"},{"match":"\\\\b(test|and|or)\\\\b","name":"keyword.default.zig"},{"match":"\\\\b(bool|void|noreturn|type|error|anyerror|anyframe|anytype|anyopaque)\\\\b","name":"keyword.type.zig"},{"match":"\\\\b(f16|f32|f64|f80|f128|u\\\\d+|i\\\\d+|isize|usize|comptime_int|comptime_float)\\\\b","name":"keyword.type.integer.zig"},{"match":"\\\\b(c_(?:char|short|ushort|int|uint|long|ulong|longlong|ulonglong|longdouble))\\\\b","name":"keyword.type.c.zig"}]},"numbers":{"patterns":[{"match":"\\\\b0x\\\\h[_\\\\h]*(\\\\.\\\\h[_\\\\h]*)?([Pp][-+]?[_\\\\h]+)?\\\\b","name":"constant.numeric.hexfloat.zig"},{"match":"\\\\b[0-9][0-9_]*(\\\\.[0-9][0-9_]*)?([Ee][-+]?[0-9_]+)?\\\\b","name":"constant.numeric.float.zig"},{"match":"\\\\b[0-9][0-9_]*\\\\b","name":"constant.numeric.decimal.zig"},{"match":"\\\\b0x[_\\\\h]+\\\\b","name":"constant.numeric.hexadecimal.zig"},{"match":"\\\\b0o[0-7_]+\\\\b","name":"constant.numeric.octal.zig"},{"match":"\\\\b0b[01_]+\\\\b","name":"constant.numeric.binary.zig"},{"match":"\\\\b[0-9](([EPep][-+])|[0-9A-Z_a-z])*(\\\\.(([EPep][-+])|[0-9A-Z_a-z])*)?([EPep][-+])?[0-9A-Z_a-z]*\\\\b","name":"constant.numeric.invalid.zig"}]},"operators":{"patterns":[{"match":"(?<=\\\\[)\\\\*c(?=])","name":"keyword.operator.c-pointer.zig"},{"match":"\\\\b((and|or))\\\\b|(==|!=|<=|>=|[<>])","name":"keyword.operator.comparison.zig"},{"match":"(-%?|\\\\+%?|\\\\*%?|[%/])=?","name":"keyword.operator.arithmetic.zig"},{"match":"(<<%?|>>|[!\\\\&^|~])=?","name":"keyword.operator.bitwise.zig"},{"match":"(==|\\\\+\\\\+|\\\\*\\\\*|->)","name":"keyword.operator.special.zig"},{"match":"=","name":"keyword.operator.assignment.zig"},{"match":"\\\\?","name":"keyword.operator.question.zig"}]},"punctuation":{"patterns":[{"match":"\\\\.","name":"punctuation.accessor.zig"},{"match":",","name":"punctuation.comma.zig"},{"match":":","name":"punctuation.separator.key-value.zig"},{"match":";","name":"punctuation.terminator.statement.zig"}]},"stringcontent":{"patterns":[{"match":"\\\\\\\\([\\"'\\\\\\\\nrt]|(x\\\\h{2})|(u\\\\{\\\\h+}))","name":"constant.character.escape.zig"},{"match":"\\\\\\\\.","name":"invalid.illegal.unrecognized-string-escape.zig"}]},"strings":{"patterns":[{"begin":"\\"","end":"\\"","name":"string.quoted.double.zig","patterns":[{"include":"#stringcontent"}]},{"begin":"\\\\\\\\\\\\\\\\","end":"$","name":"string.multiline.zig"},{"match":"'([^'\\\\\\\\]|\\\\\\\\(x\\\\h{2}|[012][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.))'","name":"string.quoted.single.zig"}]},"support":{"patterns":[{"match":"@[A-Z_a-z][0-9A-Z_a-z]*","name":"support.function.builtin.zig"}]},"variables":{"patterns":[{"name":"meta.function.declaration.zig","patterns":[{"captures":{"1":{"name":"storage.type.function.zig"},"2":{"name":"entity.name.type.zig"}},"match":"\\\\b(fn)\\\\s+([A-Z][0-9A-Za-z]*)\\\\b"},{"captures":{"1":{"name":"storage.type.function.zig"},"2":{"name":"entity.name.function.zig"}},"match":"\\\\b(fn)\\\\s+([A-Z_a-z][0-9A-Z_a-z]*)\\\\b"},{"begin":"\\\\b(fn)\\\\s+@\\"","beginCaptures":{"1":{"name":"storage.type.function.zig"}},"end":"\\"","name":"entity.name.function.string.zig","patterns":[{"include":"#stringcontent"}]},{"match":"\\\\b(const|var|fn)\\\\b","name":"keyword.default.zig"}]},{"name":"meta.function.call.zig","patterns":[{"match":"([A-Z][0-9A-Za-z]*)(?=\\\\s*\\\\()","name":"entity.name.type.zig"},{"match":"([A-Z_a-z][0-9A-Z_a-z]*)(?=\\\\s*\\\\()","name":"entity.name.function.zig"}]},{"name":"meta.variable.zig","patterns":[{"match":"\\\\b[A-Z_a-z][0-9A-Z_a-z]*\\\\b","name":"variable.zig"},{"begin":"@\\"","end":"\\"","name":"variable.string.zig","patterns":[{"include":"#stringcontent"}]}]}]}},"scopeName":"source.zig"}`));
var zig_default = [
  lang
];
export {
  zig_default as default
};
//# sourceMappingURL=zig-3RQO3BQ3.js.map
