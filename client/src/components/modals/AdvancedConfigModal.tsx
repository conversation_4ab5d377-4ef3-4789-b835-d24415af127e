/**
 * Advanced Configuration Modal for V2
 * Provides advanced settings and preferences configuration
 * Inspired by the demo HTML concept
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { SaveIcon, ResetIcon, InfoIcon } from '../ui';
import styles from './ModalContent.module.css';

interface AdvancedConfigModalProps {
  onClose: () => void;
  onBack: () => void;
}

interface AdvancedSettings {
  // Performance Settings
  maxTokens: number;
  temperature: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
  
  // Behavior Settings
  streamResponses: boolean;
  autoSave: boolean;
  confirmBeforeDelete: boolean;
  showTokenCount: boolean;
  enableKeyboardShortcuts: boolean;
  
  // Debug Settings
  enableDebugMode: boolean;
  logApiCalls: boolean;
  showResponseTimes: boolean;
  
  // UI Settings
  compactMode: boolean;
  showTimestamps: boolean;
  enableAnimations: boolean;
  fontSize: 'small' | 'medium' | 'large';
  
  // Privacy Settings
  storeConversations: boolean;
  shareAnalytics: boolean;
  clearDataOnExit: boolean;
}

const DEFAULT_ADVANCED_SETTINGS: AdvancedSettings = {
  maxTokens: 4096,
  temperature: 0.7,
  topP: 1.0,
  frequencyPenalty: 0.0,
  presencePenalty: 0.0,
  streamResponses: true,
  autoSave: true,
  confirmBeforeDelete: true,
  showTokenCount: false,
  enableKeyboardShortcuts: true,
  enableDebugMode: false,
  logApiCalls: false,
  showResponseTimes: false,
  compactMode: false,
  showTimestamps: true,
  enableAnimations: true,
  fontSize: 'medium',
  storeConversations: true,
  shareAnalytics: false,
  clearDataOnExit: false,
};

export const AdvancedConfigModal: React.FC<AdvancedConfigModalProps> = ({
  onClose,
  onBack
}) => {
  const [settings, setSettings] = useState<AdvancedSettings>(DEFAULT_ADVANCED_SETTINGS);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState<'performance' | 'behavior' | 'debug' | 'ui' | 'privacy'>('performance');

  // Load settings from store (mock for now)
  useEffect(() => {
    // In a real implementation, load from settingsStore
    const savedSettings = localStorage.getItem('sahAI_advancedSettings');
    if (savedSettings) {
      try {
        setSettings({ ...DEFAULT_ADVANCED_SETTINGS, ...JSON.parse(savedSettings) });
      } catch (error) {
        console.warn('Failed to load advanced settings:', error);
      }
    }
  }, []);

  const handleSettingChange = (key: keyof AdvancedSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = () => {
    localStorage.setItem('sahAI_advancedSettings', JSON.stringify(settings));
    setHasChanges(false);
    console.log('Advanced settings saved:', settings);
  };

  const handleReset = () => {
    setSettings(DEFAULT_ADVANCED_SETTINGS);
    setHasChanges(true);
  };

  const renderPerformanceTab = () => (
    <div className={styles['config-section']}>
      <h4 className={styles['subsection-header']}>Model Parameters</h4>
      
      <div className={styles['setting-item']}>
        <label className={styles['setting-label']}>
          Max Tokens
          <InfoIcon size={14} title="Maximum number of tokens to generate" />
        </label>
        <input
          type="number"
          min="1"
          max="32000"
          value={settings.maxTokens}
          onChange={(e) => handleSettingChange('maxTokens', parseInt(e.target.value))}
          className={styles['setting-input']}
        />
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-label']}>
          Temperature ({settings.temperature})
          <InfoIcon size={14} title="Controls randomness in responses (0.0 = deterministic, 1.0 = creative)" />
        </label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={settings.temperature}
          onChange={(e) => handleSettingChange('temperature', parseFloat(e.target.value))}
          className={styles['setting-slider']}
        />
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-label']}>
          Top P ({settings.topP})
          <InfoIcon size={14} title="Nucleus sampling parameter" />
        </label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.05"
          value={settings.topP}
          onChange={(e) => handleSettingChange('topP', parseFloat(e.target.value))}
          className={styles['setting-slider']}
        />
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-label']}>
          Frequency Penalty ({settings.frequencyPenalty})
          <InfoIcon size={14} title="Reduces repetition of tokens" />
        </label>
        <input
          type="range"
          min="-2"
          max="2"
          step="0.1"
          value={settings.frequencyPenalty}
          onChange={(e) => handleSettingChange('frequencyPenalty', parseFloat(e.target.value))}
          className={styles['setting-slider']}
        />
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-label']}>
          Presence Penalty ({settings.presencePenalty})
          <InfoIcon size={14} title="Encourages talking about new topics" />
        </label>
        <input
          type="range"
          min="-2"
          max="2"
          step="0.1"
          value={settings.presencePenalty}
          onChange={(e) => handleSettingChange('presencePenalty', parseFloat(e.target.value))}
          className={styles['setting-slider']}
        />
      </div>
    </div>
  );

  const renderBehaviorTab = () => (
    <div className={styles['config-section']}>
      <h4 className={styles['subsection-header']}>Response Behavior</h4>
      
      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.streamResponses}
            onChange={(e) => handleSettingChange('streamResponses', e.target.checked)}
          />
          Stream responses as they're generated
        </label>
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.autoSave}
            onChange={(e) => handleSettingChange('autoSave', e.target.checked)}
          />
          Auto-save conversations
        </label>
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.confirmBeforeDelete}
            onChange={(e) => handleSettingChange('confirmBeforeDelete', e.target.checked)}
          />
          Confirm before deleting conversations
        </label>
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.showTokenCount}
            onChange={(e) => handleSettingChange('showTokenCount', e.target.checked)}
          />
          Show token count in messages
        </label>
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.enableKeyboardShortcuts}
            onChange={(e) => handleSettingChange('enableKeyboardShortcuts', e.target.checked)}
          />
          Enable keyboard shortcuts
        </label>
      </div>
    </div>
  );

  const renderDebugTab = () => (
    <div className={styles['config-section']}>
      <h4 className={styles['subsection-header']}>Debug & Development</h4>
      
      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.enableDebugMode}
            onChange={(e) => handleSettingChange('enableDebugMode', e.target.checked)}
          />
          Enable debug mode
        </label>
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.logApiCalls}
            onChange={(e) => handleSettingChange('logApiCalls', e.target.checked)}
          />
          Log API calls to console
        </label>
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.showResponseTimes}
            onChange={(e) => handleSettingChange('showResponseTimes', e.target.checked)}
          />
          Show response times
        </label>
      </div>
    </div>
  );

  const renderUITab = () => (
    <div className={styles['config-section']}>
      <h4 className={styles['subsection-header']}>User Interface</h4>
      
      <div className={styles['setting-item']}>
        <label className={styles['setting-label']}>Font Size</label>
        <select
          value={settings.fontSize}
          onChange={(e) => handleSettingChange('fontSize', e.target.value)}
          className={styles['setting-select']}
        >
          <option value="small">Small</option>
          <option value="medium">Medium</option>
          <option value="large">Large</option>
        </select>
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.compactMode}
            onChange={(e) => handleSettingChange('compactMode', e.target.checked)}
          />
          Compact mode
        </label>
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.showTimestamps}
            onChange={(e) => handleSettingChange('showTimestamps', e.target.checked)}
          />
          Show message timestamps
        </label>
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.enableAnimations}
            onChange={(e) => handleSettingChange('enableAnimations', e.target.checked)}
          />
          Enable animations
        </label>
      </div>
    </div>
  );

  const renderPrivacyTab = () => (
    <div className={styles['config-section']}>
      <h4 className={styles['subsection-header']}>Privacy & Data</h4>
      
      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.storeConversations}
            onChange={(e) => handleSettingChange('storeConversations', e.target.checked)}
          />
          Store conversations locally
        </label>
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.shareAnalytics}
            onChange={(e) => handleSettingChange('shareAnalytics', e.target.checked)}
          />
          Share anonymous usage analytics
        </label>
      </div>

      <div className={styles['setting-item']}>
        <label className={styles['setting-checkbox']}>
          <input
            type="checkbox"
            checked={settings.clearDataOnExit}
            onChange={(e) => handleSettingChange('clearDataOnExit', e.target.checked)}
          />
          Clear data when extension closes
        </label>
      </div>
    </div>
  );

  return (
    <div className={styles['modal-content']}>
      <div className={styles['modal-header']}>
        <h2 className={styles['modal-title']}>Advanced Configuration</h2>
        <div className={styles['modal-actions']}>
          <button
            className={styles['reset-button']}
            onClick={handleReset}
            title="Reset to defaults"
          >
            <ResetIcon size={16} />
            Reset
          </button>
          <button
            className={`${styles['save-button']} ${hasChanges ? styles['has-changes'] : ''}`}
            onClick={handleSave}
            disabled={!hasChanges}
            title="Save changes"
          >
            <SaveIcon size={16} />
            Save
          </button>
        </div>
      </div>

      <div className={styles['modal-body']}>
        {/* Tab Navigation */}
        <div className={styles['tab-navigation']}>
          {[
            { id: 'performance', label: 'Performance' },
            { id: 'behavior', label: 'Behavior' },
            { id: 'debug', label: 'Debug' },
            { id: 'ui', label: 'UI' },
            { id: 'privacy', label: 'Privacy' }
          ].map(tab => (
            <button
              key={tab.id}
              className={`${styles['tab-button']} ${activeTab === tab.id ? styles['active'] : ''}`}
              onClick={() => setActiveTab(tab.id as any)}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className={styles['tab-content']}>
          {activeTab === 'performance' && renderPerformanceTab()}
          {activeTab === 'behavior' && renderBehaviorTab()}
          {activeTab === 'debug' && renderDebugTab()}
          {activeTab === 'ui' && renderUITab()}
          {activeTab === 'privacy' && renderPrivacyTab()}
        </div>

        {hasChanges && (
          <div className={styles['changes-indicator']}>
            <InfoIcon size={16} />
            You have unsaved changes. Click Save to apply them.
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedConfigModal;
