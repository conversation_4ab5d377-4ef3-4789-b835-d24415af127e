/**
 * Multi-Model Chat Modal for V2
 * Allows users to chat with multiple AI models simultaneously
 * Inspired by the demo HTML concept
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { useChatStore } from '../../stores/chatStore';
import { PlusIcon, MinusIcon, SendIcon, CompareIcon } from '../ui';
import styles from './ModalContent.module.css';

interface MultiModelModalProps {
  onClose: () => void;
  onBack: () => void;
}

interface ModelResponse {
  modelId: string;
  modelName: string;
  providerName: string;
  response: string;
  responseTime: number;
  isLoading: boolean;
  error?: string;
}

interface MultiModelSession {
  id: string;
  prompt: string;
  selectedModels: string[];
  responses: ModelResponse[];
  timestamp: Date;
}

export const MultiModelModal: React.FC<MultiModelModalProps> = ({
  onClose,
  onBack
}) => {
  const { providers, currentProvider } = useSettingsStore();
  const { createNewSession } = useChatStore();
  
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [currentPrompt, setCurrentPrompt] = useState('');
  const [sessions, setSessions] = useState<MultiModelSession[]>([]);
  const [activeSession, setActiveSession] = useState<MultiModelSession | null>(null);
  const [isComparing, setIsComparing] = useState(false);

  // Get available models from all providers
  const availableModels = React.useMemo(() => {
    return providers.flatMap(provider => {
      // Mock models for each provider since models property doesn't exist yet
      const mockModels = [
        { id: 'default', name: `${provider.name} Default Model` }
      ];

      return mockModels.map(model => ({
        id: `${provider.id}:${model.id}`,
        name: model.name,
        providerName: provider.name,
        providerId: provider.id,
        isConfigured: provider.isConfigured
      }));
    });
  }, [providers]);

  useEffect(() => {
    // Auto-select current model if available
    if (currentProvider && availableModels.length > 0) {
      const currentModelId = `${currentProvider.id}:default`;
      if (availableModels.some(m => m.id === currentModelId)) {
        setSelectedModels([currentModelId]);
      }
    }
  }, [currentProvider, availableModels]);

  const handleModelToggle = (modelId: string) => {
    setSelectedModels(prev => {
      if (prev.includes(modelId)) {
        return prev.filter(id => id !== modelId);
      } else if (prev.length < 6) { // Limit to 6 models
        return [...prev, modelId];
      }
      return prev;
    });
  };

  const simulateModelResponse = async (modelId: string, prompt: string): Promise<ModelResponse> => {
    const model = availableModels.find(m => m.id === modelId);
    if (!model) {
      throw new Error('Model not found');
    }

    const startTime = Date.now();
    
    // Simulate different response times and styles for different models
    const responseTime = 1000 + Math.random() * 3000; // 1-4 seconds
    await new Promise(resolve => setTimeout(resolve, responseTime));
    
    const actualResponseTime = Date.now() - startTime;

    // Generate mock responses based on model type
    let response = '';
    if (model.name.toLowerCase().includes('claude')) {
      response = `Claude's thoughtful response to: "${prompt}"\n\nI'll approach this systematically and provide a comprehensive analysis...`;
    } else if (model.name.toLowerCase().includes('gpt')) {
      response = `GPT response to: "${prompt}"\n\nHere's my take on this question, breaking it down into key components...`;
    } else if (model.name.toLowerCase().includes('gemini')) {
      response = `Gemini's response to: "${prompt}"\n\nLet me provide you with accurate information and helpful insights...`;
    } else {
      response = `${model.name} response to: "${prompt}"\n\nBased on my training, here's what I can tell you...`;
    }

    return {
      modelId,
      modelName: model.name,
      providerName: model.providerName,
      response,
      responseTime: actualResponseTime,
      isLoading: false
    };
  };

  const handleSendPrompt = async () => {
    if (!currentPrompt.trim() || selectedModels.length === 0) return;

    const sessionId = `session-${Date.now()}`;
    const newSession: MultiModelSession = {
      id: sessionId,
      prompt: currentPrompt,
      selectedModels: [...selectedModels],
      responses: selectedModels.map(modelId => {
        const model = availableModels.find(m => m.id === modelId)!;
        return {
          modelId,
          modelName: model.name,
          providerName: model.providerName,
          response: '',
          responseTime: 0,
          isLoading: true
        };
      }),
      timestamp: new Date()
    };

    setSessions(prev => [newSession, ...prev]);
    setActiveSession(newSession);
    setIsComparing(true);

    // Generate responses for each model
    const responsePromises = selectedModels.map(async (modelId) => {
      try {
        const response = await simulateModelResponse(modelId, currentPrompt);
        
        setSessions(prev => prev.map(session => {
          if (session.id === sessionId) {
            return {
              ...session,
              responses: session.responses.map(r => 
                r.modelId === modelId ? response : r
              )
            };
          }
          return session;
        }));
      } catch (error) {
        setSessions(prev => prev.map(session => {
          if (session.id === sessionId) {
            return {
              ...session,
              responses: session.responses.map(r => 
                r.modelId === modelId ? {
                  ...r,
                  isLoading: false,
                  error: error instanceof Error ? error.message : 'Unknown error'
                } : r
              )
            };
          }
          return session;
        }));
      }
    });

    await Promise.all(responsePromises);
    setCurrentPrompt('');
  };

  const renderModelSelector = () => (
    <div className={styles['model-selector']}>
      <h4 className={styles['subsection-header']}>
        Select Models ({selectedModels.length}/6)
      </h4>
      <div className={styles['model-chips']}>
        {availableModels.map(model => (
          <button
            key={model.id}
            className={`${styles['model-chip']} ${
              selectedModels.includes(model.id) ? styles['selected'] : ''
            } ${!model.isConfigured ? styles['disabled'] : ''}`}
            onClick={() => model.isConfigured && handleModelToggle(model.id)}
            disabled={!model.isConfigured}
            title={!model.isConfigured ? 'Provider not configured' : ''}
          >
            <span className={styles['model-chip-name']}>{model.name}</span>
            <span className={styles['model-chip-provider']}>{model.providerName}</span>
            {selectedModels.includes(model.id) ? (
              <MinusIcon size={12} className={styles['chip-icon'] || ''} />
            ) : (
              <PlusIcon size={12} className={styles['chip-icon'] || ''} />
            )}
          </button>
        ))}
      </div>
    </div>
  );

  const renderPromptInput = () => (
    <div className={styles['prompt-input-section']}>
      <h4 className={styles['subsection-header']}>Enter Your Prompt</h4>
      <div className={styles['prompt-input-wrapper']}>
        <textarea
          value={currentPrompt}
          onChange={(e) => setCurrentPrompt(e.target.value)}
          placeholder="Ask a question to compare responses from multiple models..."
          className={styles['prompt-textarea']}
          rows={3}
        />
        <button
          className={styles['send-button']}
          onClick={handleSendPrompt}
          disabled={!currentPrompt.trim() || selectedModels.length === 0}
          title="Send to all selected models"
        >
          <SendIcon size={16} />
          Send to {selectedModels.length} models
        </button>
      </div>
    </div>
  );

  const renderResponse = (response: ModelResponse) => (
    <div key={response.modelId} className={styles['response-card']}>
      <div className={styles['response-header']}>
        <h5 className={styles['response-model-name']}>
          {response.modelName}
          <span className={styles['response-provider']}>({response.providerName})</span>
        </h5>
        {!response.isLoading && !response.error && (
          <span className={styles['response-time']}>
            {(response.responseTime / 1000).toFixed(1)}s
          </span>
        )}
      </div>
      
      <div className={styles['response-content']}>
        {response.isLoading ? (
          <div className={styles['loading-indicator']}>
            <div className={styles['loading-spinner']}></div>
            <span>Generating response...</span>
          </div>
        ) : response.error ? (
          <div className={styles['error-message']}>
            <span className={styles['error-icon']}>⚠️</span>
            <span>Error: {response.error}</span>
          </div>
        ) : (
          <div className={styles['response-text']}>
            {response.response}
          </div>
        )}
      </div>
    </div>
  );

  const renderComparison = () => {
    if (!activeSession) return null;

    return (
      <div className={styles['comparison-section']}>
        <div className={styles['comparison-header']}>
          <h4 className={styles['subsection-header']}>
            <CompareIcon size={16} />
            Comparing Responses
          </h4>
          <div className={styles['comparison-meta']}>
            <span className={styles['prompt-preview']}>
              "{activeSession.prompt.length > 100 
                ? activeSession.prompt.substring(0, 100) + '...' 
                : activeSession.prompt}"
            </span>
            <span className={styles['comparison-time']}>
              {activeSession.timestamp.toLocaleTimeString()}
            </span>
          </div>
        </div>
        
        <div className={styles['responses-grid']}>
          {activeSession.responses.map(renderResponse)}
        </div>
      </div>
    );
  };

  const renderSessionHistory = () => (
    <div className={styles['session-history']}>
      <h4 className={styles['subsection-header']}>Previous Comparisons</h4>
      {sessions.length === 0 ? (
        <p className={styles['no-sessions']}>No previous comparisons</p>
      ) : (
        <div className={styles['session-list']}>
          {sessions.slice(1).map(session => (
            <button
              key={session.id}
              className={`${styles['session-item']} ${
                activeSession?.id === session.id ? styles['active'] : ''
              }`}
              onClick={() => setActiveSession(session)}
            >
              <div className={styles['session-prompt']}>
                {session.prompt.length > 80 
                  ? session.prompt.substring(0, 80) + '...' 
                  : session.prompt}
              </div>
              <div className={styles['session-meta']}>
                <span>{session.selectedModels.length} models</span>
                <span>{session.timestamp.toLocaleTimeString()}</span>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className={styles['modal-content']}>
      <div className={styles['modal-header']}>
        <h2 className={styles['modal-title']}>Multi-Model Chat</h2>
        <div className={styles['modal-actions']}>
          <span className={styles['selected-count']}>
            {selectedModels.length} models selected
          </span>
        </div>
      </div>

      <div className={styles['modal-body']}>
        {!isComparing ? (
          <>
            {renderModelSelector()}
            {renderPromptInput()}
            {sessions.length > 0 && renderSessionHistory()}
          </>
        ) : (
          <>
            <button
              className={styles['back-to-setup']}
              onClick={() => setIsComparing(false)}
            >
              ← Back to Setup
            </button>
            {renderComparison()}
          </>
        )}
      </div>
    </div>
  );
};

export default MultiModelModal;
